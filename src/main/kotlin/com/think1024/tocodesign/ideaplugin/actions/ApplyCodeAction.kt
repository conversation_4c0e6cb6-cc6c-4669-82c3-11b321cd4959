package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.diff.editor.DiffContentVirtualFile
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.DiffDataKeys
import com.intellij.diff.util.Side
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.DumbAwareAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import javax.swing.Icon


open class ApplyCodeAction(text: String,
                      description: String,
                      icon: Icon,
                      protected val targetFile: VirtualFile,
                      private val textToApply: String): DumbAwareAction(text, description, icon) {

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.EDT
    }

    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = true
    }

    override fun actionPerformed(e: AnActionEvent) {
        val diffViewer = e.getData(DiffDataKeys.DIFF_VIEWER) ?: return
        if (diffViewer is SimpleDiffViewer) {
            // 获取右侧编辑器的当前内容（可能已被用户编辑）
            val currentRightContent = diffViewer.editor2.document.text
            this.handleSimpleDiffViewer(diffViewer, currentRightContent)
        } else if (diffViewer is UnifiedDiffViewer) {
            // 对于UnifiedDiffViewer，使用原始逻辑
            this.handleUnifiedDiffViewer(diffViewer, this.textToApply)
        }
        val project = e.project
        if (project != null) {
            this.closeDiffViewer(project)
        }
    }

    private fun closeDiffViewer(project: Project) {
        val editorManager = FileEditorManager.getInstance(project)
        val virtualFileArray = editorManager.selectedFiles
        val currentFile = virtualFileArray.firstOrNull()
        if (currentFile is DiffContentVirtualFile) {
            editorManager.closeFile(currentFile)
        }
        editorManager.openFile(this.targetFile, true)
    }

    override fun displayTextInToolbar(): Boolean {
        return true
    }

    private fun handleSimpleDiffViewer(viewer: SimpleDiffViewer, text: String) {
        val document = viewer.editor1.document
        WriteCommandAction.runWriteCommandAction(viewer.project) {
            document.setText(text)
        }
    }

    private fun handleUnifiedDiffViewer(viewer: UnifiedDiffViewer, text: String) {
        val document = viewer.getDocument(Side.LEFT)
        WriteCommandAction.runWriteCommandAction(viewer.project) {
            document.setText(text)
            viewer.rediff()
        }
    }

}