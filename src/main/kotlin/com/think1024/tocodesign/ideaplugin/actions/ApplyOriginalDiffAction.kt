package com.think1024.tocodesign.ideaplugin.actions

import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.DiffDataKeys
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.DumbAwareAction
import com.intellij.openapi.vfs.VirtualFile
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString

/**
 * 专门用于原始diff的Apply操作，会将右侧编辑器的内容应用到目标文件
 */
class ApplyOriginalDiffAction(
    private val targetFile: VirtualFile
) : DumbAwareAction(
    getI18nString("code.apply.accept.all"),
    getI18nString("code.apply.accept.all.description"),
    AllIcons.General.InspectionsOK
) {

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.EDT
    }

    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = true
    }

    override fun actionPerformed(e: AnActionEvent) {
        val diffViewer = e.getData(DiffDataKeys.DIFF_VIEWER) ?: return
        
        if (diffViewer is SimpleDiffViewer) {
            // 获取右侧编辑器的当前内容（可能已被用户编辑）
            val currentRightContent = diffViewer.editor2.document.text
            
            // 在写入操作中保存文件
            WriteCommandAction.runWriteCommandAction(diffViewer.project) {
                try {
                    // 方法1：直接设置文件内容
                    targetFile.setBinaryContent(currentRightContent.toByteArray(Charsets.UTF_8))
                    
                    // 方法2：通过Document保存（备用方案）
                    val document = FileDocumentManager.getInstance().getDocument(targetFile)
                    if (document != null) {
                        document.setText(currentRightContent)
                        FileDocumentManager.getInstance().saveDocument(document)
                    }
                    
                    // 刷新文件系统
                    targetFile.refresh(false, false)
                    
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            
            // 关闭diff窗口
            val project = e.project
            if (project != null) {
                ApplicationManager.getApplication().invokeLater {
                    closeDiffViewer(project)
                }
            }
        }
    }

    private fun closeDiffViewer(project: com.intellij.openapi.project.Project) {
        val editorManager = com.intellij.openapi.fileEditor.FileEditorManager.getInstance(project)
        val virtualFileArray = editorManager.selectedFiles
        val currentFile = virtualFileArray.firstOrNull()
        if (currentFile is com.intellij.diff.editor.DiffContentVirtualFile) {
            editorManager.closeFile(currentFile)
        }
        editorManager.openFile(this.targetFile, true)
    }

    override fun displayTextInToolbar(): Boolean {
        return true
    }
}
