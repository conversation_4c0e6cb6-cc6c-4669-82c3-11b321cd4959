package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import java.util.concurrent.ConcurrentHashMap

/**
 * 存储文件原始版本的服务类
 */
@Service(Service.Level.PROJECT)
class FileOriginalVersionService(private val project: Project) {
    private val logger = Logger.getInstance(FileOriginalVersionService::class.java)
    private val fileVersions = ConcurrentHashMap<String, String>()

    /**
     * 记录文件的原始版本（如果尚未记录）
     *
     * @param filePath 文件路径
     * @return 是否成功记录（如果已存在记录则返回false）
     */
    fun recordOriginalVersion(filePath: String): Boolean {
        // 如果已经记录过该文件的原始版本，则不再记录
        if (fileVersions.containsKey(filePath)) {
            return false
        }

        // 获取文件当前内容
        val codeFileService = CodeFileService.getInstance(project)
        // 如果拿不到内容，意味着新文件
        val content = codeFileService.getFileContent(filePath) ?: ""

        // 记录原始版本
        fileVersions[filePath] = content
        logger.info("记录文件原始版本: $filePath")
        logger.info("记录文件原始内容: $content")

        return true
    }

    /**
     * 获取文件的原始版本内容
     *
     * @param filePath 文件路径
     * @return 原始版本内容，如果不存在则返回null
     */
    fun getOriginalVersion(filePath: String): String? {
        return fileVersions[filePath]
    }

    /**
     * 显示指定文件的原始版本和当前版本的差异
     *
     * @param filePath 文件路径
     * @return 是否成功显示差异（如果没有原始版本则返回false）
     */
    fun showOriginalDiff(filePath: String, originContent: String? = null): Boolean {
        // 获取原始版本
        val readOriginContent = originContent ?: fileVersions[filePath] ?: return false

        // 用于测试
        if (originContent != null && fileVersions[filePath] == null) {
            fileVersions[filePath] = originContent
        }

        // 获取当前版本
        val codeFileService = CodeFileService.getInstance(project)
        val currentContent = codeFileService.getFileContent(filePath) ?: return false
        val virtualFile = FileUtil.getVirtualFile(filePath, project)

        // 显示差异
        ApplicationManager.getApplication().invokeLater {
            val diffRequest = SimpleDiffRequest(
                "${getI18nString("code.diff.title.file.diff")}: ${FileUtil.getVirtualFile(filePath, project)?.name ?: filePath}",
                DiffContentFactory.getInstance().create(readOriginContent),
                DiffContentFactory.getInstance().create(currentContent, virtualFile),
                getI18nString("code.diff.origin.version"),
                getI18nString("code.diff.current.version"),
            )

            DiffManager.getInstance().showDiff(project, diffRequest)
        }

        return true
    }

    /**
     * 在源文件窗口中显示内联diff
     *
     * @param filePath 文件路径
     * @return 是否成功显示内联diff
     */
    fun showInlineDiff(filePath: String, originContent: String? = null): Boolean {
        // 获取原始版本
        val readOriginContent = originContent ?: fileVersions[filePath] ?: return false

        if (originContent != null && fileVersions[filePath] == null) {
            fileVersions[filePath] = originContent
        }

        // 使用内联diff服务
        val inlineDiffService = project.service<InlineDiffService>()
        return inlineDiffService.showInlineDiff(filePath, readOriginContent)
    }

    /**
     * 清除指定文件的原始版本记录
     */
    fun clearOriginalVersion(filePath: String) {
        fileVersions.remove(filePath)
    }

    /**
     * 判断文件是否有原始版本记录
     */
    fun hasOriginalVersion(filePath: String): Boolean {
        return fileVersions.containsKey(filePath)
    }

    companion object {
        /**
         * 获取服务实例
         */
        fun getInstance(project: Project): FileOriginalVersionService = project.service()
    }
}