package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.diff.DiffContext
import com.intellij.diff.DiffExtension
import com.intellij.diff.FrameDiffTool
import com.intellij.diff.requests.DiffRequest
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.base.IgnorePolicy
import com.intellij.diff.tools.util.base.TextDiffSettingsHolder
import com.intellij.openapi.util.Key

val TOCO_APPLY_CODE_KEY = Key.create<Boolean>("Toco.ApplyCode")

class TocoDiffExtension : DiffExtension() {
    override fun onViewerCreated(
        viewer: FrameDiffTool.DiffViewer,
        context: DiffContext,
        request: DiffRequest
    ) {
        if (request.getUserData(TOCO_APPLY_CODE_KEY) == true) {
            val settings = context.getUserData(TextDiffSettingsHolder.TextDiffSettings.Companion.KEY)
            settings?.let { it.ignorePolicy = IgnorePolicy.IGNORE_WHITESPACES_CHUNKS }

            // 确保右侧编辑器是可编辑的
            if (viewer is SimpleDiffViewer) {
                // 右侧编辑器（editor2）设置为可编辑
                viewer.editor2.settings.isVirtualSpace = false
                viewer.editor2.document.setReadOnly(false)
            }
        }
    }
}